package chpc

import (
	"context"
	"fmt"
	"time"

	"icode.baidu.com/helix_web/helpers"

	bceauth "github.com/baidubce/bce-sdk-go/auth"
	bcehttp "github.com/baidubce/bce-sdk-go/http"

	"icode.baidu.com/baidu/bce-iam/sdk-go/auth"
	"icode.baidu.com/baidu/bce-iam/sdk-go/iam"
)

const (
	ObjectURLExpireTime = 300 // 300s
)

var (
	iamClient *iam.BceClient
	iamCofig  *iam.BceClientConfiguration
	stsConfig *iam.StsClientConfiguration
)

type StsConfig struct {
	Endpoint string
	Version  string
}

func init() {
	// err := conf.Parse(env.ConfDir()+"/iam.toml", &iamCofig)
	// if err != nil {
	// 	panic(err)
	// }
	iamCofig = &iam.BceClientConfiguration{}
	iamCofig.Retry = &iam.NoRetryPolicy{}
	iamCofig.Endpoint = "http://iam.bj.bce-internal.baidu.com:80"
	iamCofig.UserName = "console_chpc"
	iamCofig.Password = "1aP5ZRTrFjV1wQkOsz9eZf9u1sLsEujV"
	iamCofig.Version = "/v3"
	iamCofig.Domain = "Default"
	iamClient = iam.NewBceClient(iamCofig)
	// err := conf.Parse(env.ConfDir()+"/sts.toml", &stsConfig)
	// if err != nil {
	// 	panic(err)
	// }
	stsConfig = &iam.StsClientConfiguration{}
	stsConfig.Endpoint = "http://sts.bj.iam.sdns.baidu.com:8586"
	stsConfig.Version = "/v1"
}

// newStsClient 创建sts客户端
func newStsClient() *iam.StsBceClient {
	serviceToken, err := iamClient.GetConsoleToken(false, "")
	if err != nil {
		panic(fmt.Sprintf("Failed to GetConsoleToken: %+v", err))
	}
	serviceAccessKeys, err := iamClient.GetAccessKeys(serviceToken.User.ID, false)
	if len(serviceAccessKeys.AccessKeys) == 0 {
		panic(fmt.Sprintf("Failed to GetAccessKeys by service account: %+v", err))
	}
	StsConfig := &iam.StsClientConfiguration{
		Endpoint: stsConfig.Endpoint,
		Version:  stsConfig.Version,
		Credentials: &auth.BceCredentials{
			AccessKeyId:     serviceAccessKeys.AccessKeys[0].Access,
			SecretAccessKey: serviceAccessKeys.AccessKeys[0].Secret,
		},
		SignOption: &auth.SignOptions{
			HeadersToSign: auth.DEFAULT_HEADERS_TO_SIGN,
			Timestamp:     time.Now().UTC().Unix(),
			ExpireSeconds: auth.DEFAULT_EXPIRE_SECONDS,
			IsBce:         true, // 必须设置为true
		},
		Retry: &iam.NoRetryPolicy{},
	}
	return iam.NewStsBceClient(StsConfig)
}

// ParseCredentialFromUserDomainID STS modify, 获取临时凭证
func ParseCredentialFromUserDomainID(iamUserDomainID string) (*iam.Credential, error) {
	assumeRoleArgs := iam.AssumeRoleArgs{
		DurationSeconds: 7200,
		AccountID:       iamUserDomainID,
		RoleName:        "BceServiceRole_console_chpc",
	}
	stsClient := newStsClient()
	credential, err := stsClient.AssumeRole(assumeRoleArgs)
	if err != nil {
		fmt.Println("Failed to AssumeRole:", err)
		return nil, err
	}
	return credential, nil
}

// ParseCredentialFromUserID STS modify, 获取临时凭证，子用户使用
func ParseCredentialFromUserID(iamUserDomainID string, iamUserID string) (*iam.Credential, error) {
	assumeRoleArgs := iam.AssumeRoleArgs{
		DurationSeconds: 7200,
		AccountID:       iamUserDomainID,
		RoleName:        "BceServiceRole_console_chpc",
		UserID:          iamUserID,
	}
	stsClient := newStsClient()
	credential, err := stsClient.AssumeRole(assumeRoleArgs)
	if err != nil {
		fmt.Println("Failed to AssumeRole:", err)
		return nil, err
	}
	return credential, nil
}

// IsOpenCHPCService 判断是否开启了CHPC服务，返回bool类型的值，true表示已经开启，false表示未开启
// 参数：
//
//	iamUserDomainID string - IAM用户所在的用户域ID，格式为"<user_id>-<domain_id>"
//
// 返回值：
//
//	bool - 如果IAM用户所在的用户域ID包含有效的且非空的AK/SK信息，则返回true；否则返回false
func IsOpenCHPCService(iamUserDomainID string) bool {
	if _, err := ParseCredentialFromUserDomainID(iamUserDomainID); err != nil {
		return false
	}
	return true
}

// GenerateStsAuthHeader STS modify, 传入临时凭证并签名
func GenerateStsAuthHeader(req *bcehttp.Request, ak, sk string) {
	cred, _ := bceauth.NewBceCredentials(ak, sk)
	opts := &bceauth.SignOptions{
		HeadersToSign: map[string]struct{}{
			"host": {},
		},
		ExpireSeconds: ObjectURLExpireTime,
	}
	signer := bceauth.BceV1Signer{}
	signer.Sign(req, cred, opts)
}

// AssembleStsRequest 将请求信息组装成STS请求，并设置相应的头部和签名信息
// ctx: 上下文对象，可以为nil
// req: *bcehttp.Request类型，需要组装的请求对象
// accountID: string类型，账户ID，用于解析用户域ID获取STS凭证信息
// method: string类型，HTTP方法，如"GET"、"POST"等
// endpoint: string类型，服务器地址，如"sts.bj.baidubce.com"
// uri: string类型，URI路径，如"/v1/session"
// 无返回值
func AssembleStsRequest(ctx context.Context, req *bcehttp.Request, accountID, method, endpoint, uri string) {
	var (
		ak           = "ALTAKN6wG2vrEi7wVATa31qUk3"
		sk           = "7f7d55a59a044cb39ae4ecb2b224b100"
		sessionToken = ""
	)
	credential, err := ParseCredentialFromUserDomainID(accountID)
	if err != nil {
		helpers.LogError(ctx, fmt.Errorf("during assemble sts request, parse credential of account %s failed, "+
			"err is %v", accountID, err))
	} else {
		ak, sk, sessionToken = credential.AccessKeyID, credential.SecretAccessKey, credential.SessionToken
	}

	clientConf := bcehttp.ClientConfig{}
	bcehttp.InitClient(clientConf)
	req.SetProtocol("http")
	req.SetMethod(method)
	req.SetHost(endpoint)
	req.SetUri(uri)
	req.SetHeader("Host", endpoint)
	req.SetHeader("Content-Type", "application/json")
	if sessionToken != "" {
		req.SetHeader("x-bce-security-token", credential.SessionToken)
	}
	item := CreateOrderItem{ // hf3
		ServiceType:    "CHPC",
		ProductType:    "prepay",
		SubProductType: "project",
		Key:            "helixfold3-service",
		Count:          1,
		Flavor: []Flavor{
			{
				Name:  "subServiceType",
				Value: "HelixFold3",
				Scale: 1,
			},
			{
				Name:  "version",
				Value: "1",
				Scale: 1,
			},
		},
		Time:     1,
		TimeUnit: "DAY",
		Extra:    "paddlehelix_infer_12345",
	}
	body := CreateOrderRequest{
		Region:      "global",
		OrderType:   OrederTypeNew,
		Items:       []CreateOrderItem{item},
		IsDirectPay: false,
		DirectPayCouponConfig: DirectPayCouponConfig{
			Type:       "AUTO",
			ScopeValue: "COMMON",
		},
	}
	req.SetBody(body)
	GenerateStsAuthHeader(req, ak, sk)
}
