package services

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"strconv"
	"strings"

	"icode.baidu.com/helix_web/library/chpc"
	"icode.baidu.com/helix_web/library/ctxutils"

	bcehttp "github.com/baidubce/bce-sdk-go/http"
	"icode.baidu.com/baidu/gdp/codec"
	"icode.baidu.com/baidu/gdp/env"
	"icode.baidu.com/baidu/gdp/ghttp"
	"icode.baidu.com/baidu/gdp/net/ral"
	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
	"icode.baidu.com/helix_web/models"
)

// 任务类型转换
const (
	TaskTypeFold              int = 300
	TaskTypePartition         int = 301
	TaskTypeMRNA              int = 310
	TaskTypeAdmet             int = 320
	TaskTypeMolActivity       int = 330
	TaskTypeMolActStruct      int = 331
	TaskTypeMMGBSA            int = 332
	TaskTypeHelixFoldAA       int = 333
	TaskTypeMolFormation      int = 340
	TaskTypeMolFormPath       int = 340
	TaskTypeMolFrame          int = 341
	TaskTypeProtein           int = 350
	TaskTypeProteinSingle     int = 351
	TaskTypeProteinRelaxation int = 352
	TaskTypeVirtualFilter     int = 360
	TaskTypeVirtualVS         int = 361
	TaskTypeHelixDock         int = 362
	TaskTypeDoubleDrug        int = 370
	TaskTypeAntibody          int = 380 // 抗原
	TaskTypeNewCrown          int = 381 // 新冠
	TaskTypeProteinMutation   int = 382 // 蛋白突变
	TaskTypeKYKT              int = 383 // 蛋白突变
	TaskTypeProteinFunc       int = 390 // 蛋白质功能
	TaskTypeExactDrug         int = 400 // 精准用药
	TaskTypeCompound          int = 410 // 分子逆合成
	TaskTypeUTR               int = 420 // utr
	TaskTypeProteinComplex    int = 430 // 蛋白复合物
)

var TaskTypeMap = map[int]int{
	models.TaskTypeLinearFold:        TaskTypeFold,
	models.TaskTypeLinearPartition:   TaskTypePartition,
	models.TaskTypeLinearDesign:      TaskTypeMRNA,
	models.TaskTypeAdmet:             TaskTypeAdmet,
	models.TaskTypeSelfAdmet:         TaskTypeAdmet,
	models.TaskTypeMolActivity:       TaskTypeMolActivity,
	models.TaskTypeMolActStruct:      TaskTypeMolActStruct,
	models.TaskTypeMMGBSA:            TaskTypeMMGBSA,
	models.TaskTypeMolFormation:      TaskTypeMolFormation,
	models.TaskTypeMolFormPath:       TaskTypeMolFormPath,
	models.TaskTypeHelixVSSyn:        TaskTypeMolFrame,
	models.TaskTypeProtein:           TaskTypeProtein,
	models.TaskTypeProteinSingle:     TaskTypeProteinSingle,
	models.TaskTypeVirtualFilter:     TaskTypeVirtualFilter,
	models.TaskTypeVirtualVS:         TaskTypeVirtualVS,
	models.TaskTypeHelixDock:         TaskTypeHelixDock,
	models.TaskTypeDoubleDrug:        TaskTypeDoubleDrug,
	models.TaskTypeAntibody:          TaskTypeAntibody,
	models.TaskTypeNewCrown:          TaskTypeNewCrown,
	models.TaskTypeProteinFunc:       TaskTypeProteinFunc,
	models.TaskTypeProteinMutation:   TaskTypeProteinMutation,
	models.TaskTypeExactDrug:         TaskTypeExactDrug,
	models.TaskTypeCompound:          TaskTypeCompound,
	models.TaskTypeUTR:               TaskTypeUTR,
	models.TaskTypeProteinComplex:    TaskTypeProteinComplex,
	models.TaskTypeKYKT:              TaskTypeKYKT,
	models.TaskTypeProteinRelaxation: TaskTypeProteinRelaxation,
	models.TaskTypeHelixFoldAA:       TaskTypeHelixFoldAA,
}

const (
	defaultTaskPrefix   = "paddlehelix_"
	defaultFramework    = "custom"
	defaultCodePath     = ""
	defaultStartCmd     = "/rudder_code/3.7/rudder/rudder_paddlehelix/scripts/start.sh"
	defaultTrainsetPath = ""
	defaultCloudId      = "6c6093c96f0241c087af184cc5729de8"
	defaultTrainerCount = 1
	defaultLanguage     = "bash"
	defaultUserTimeout  = 259200
	defaultTestsetPath  = ""
	DokaVmType          = 14
	DokaSerialLen       = 1000
)

const (
	// helixVS 任务参数
	helixVSFramework = "helixvs"
	helixVSStartCmd  = "/root/helix_vs/start.sh"
)

var VmTypeMap = map[int]int64{
	models.TaskTypeLinearFold:        7,
	models.TaskTypeLinearPartition:   7,
	models.TaskTypeLinearDesign:      7,
	models.TaskTypeAdmet:             1,
	models.TaskTypeSelfAdmet:         1,
	models.TaskTypeMolActivity:       1,
	models.TaskTypeMolActStruct:      1, // v100
	models.TaskTypeMMGBSA:            1, // P40
	models.TaskTypeMolFormation:      8,
	models.TaskTypeMolFormPath:       14,
	models.TaskTypeHelixVSSyn:        18,
	models.TaskTypeProtein:           1,
	models.TaskTypeProteinSingle:     1,
	models.TaskTypeVirtualFilter:     1,
	models.TaskTypeVirtualVS:         1,
	models.TaskTypeDoubleDrug:        8,
	models.TaskTypeAntibody:          8,
	models.TaskTypeNewCrown:          8,
	models.TaskTypeProteinFunc:       1,
	models.TaskTypeProteinMutation:   8,
	models.TaskTypeKYKT:              1,
	models.TaskTypeExactDrug:         8,
	models.TaskTypeCompound:          1,
	models.TaskTypeUTR:               8,
	models.TaskTypeProteinComplex:    1,
	models.TaskTypeHelixDock:         1,
	models.TaskTypeProteinRelaxation: 1,
	models.TaskTypeHelixFoldAA:       1,
}

var ImageEnvMap = map[string]string{
	env.RunModeRelease: "online",
	env.RunModeTest:    "qa",
	env.RunModeDebug:   "dev",
}
var ImageMap = map[int]string{
	models.TaskTypeLinearFold:        "registry.baidubce.com/rudder/paddlehelix/linear:2.0-",
	models.TaskTypeLinearPartition:   "registry.baidubce.com/rudder/paddlehelix/linear:2.0-",
	models.TaskTypeLinearDesign:      "registry.baidubce.com/rudder/paddlehelix/lineardesign_adv:2.0-",
	models.TaskTypeAdmet:             "registry.baidubce.com/rudder/paddlehelix/admet:2.0-",
	models.TaskTypeSelfAdmet:         "registry.baidubce.com/rudder/paddlehelix/admet:2.0-",
	models.TaskTypeMolActivity:       "registry.baidubce.com/rudder/paddlehelix/dti:2.0-",
	models.TaskTypeMolFormation:      "registry.baidubce.com/rudder/paddlehelix/molecular_generation:2.0-",
	models.TaskTypeMolFormPath:       "registry.baidubce.com/rudder/paddlehelix/route_syn:2.0-",
	models.TaskTypeHelixVSSyn:        "registry.baidubce.com/rudder/paddlehelix/scaffold:2.0-",
	models.TaskTypeProtein:           "registry.baidubce.com/rudder/paddlehelix/paddlefold:2.0-",
	models.TaskTypeProteinSingle:     "registry.baidubce.com/rudder/paddlehelix/helixfold_single:2.0-",
	models.TaskTypeVirtualFilter:     "registry.baidubce.com/rudder/paddlehelix/virtual_screening:2.0-",
	models.TaskTypeVirtualVS:         "registry.baidubce.com/rudder/train_custom/helix_vs1.0.0_ubuntu16.04-python3:custom-job_",
	models.TaskTypeHelixDock:         "registry.baidubce.com/rudder/paddlehelix/helix_dock:2.0-",
	models.TaskTypeMMGBSA:            "registry.baidubce.com/rudder/paddlehelix/mmgbsa_ligand:2.0-",
	models.TaskTypeDoubleDrug:        "registry.baidubce.com/rudder/paddlehelix/drugcomb:2.0-",
	models.TaskTypeAntibody:          "registry.baidubce.com/rudder/paddlehelix/ppi_s2f:2.0-",
	models.TaskTypeNewCrown:          "registry.baidubce.com/rudder/paddlehelix/ppi_s2f:2.0-",
	models.TaskTypeProteinFunc:       "registry.baidubce.com/rudder/paddlehelix/helixpfp:2.0-",
	models.TaskTypeProteinMutation:   "registry.baidubce.com/rudder/paddlehelix/ppi_s2f:2.1-",
	models.TaskTypeKYKT:              "registry.baidubce.com/rudder/paddlehelix/helixfold_multimer_abag:2.0-",
	models.TaskTypeMolActStruct:      "registry.baidubce.com/rudder/paddlehelix/dta:2.0-",
	models.TaskTypeExactDrug:         "registry.baidubce.com/rudder/paddlehelix/drug_reporpose:2.0-",
	models.TaskTypeCompound:          "registry.baidubce.com/rudder/paddlehelix/retrosynthesis:2.0-",
	models.TaskTypeUTR:               "registry.baidubce.com/rudder/paddlehelix/utr_design:2.0-",
	models.TaskTypeProteinComplex:    "registry.baidubce.com/rudder/paddlehelix/helixfold_multimer:2.0-",
	models.TaskTypeProteinRelaxation: "registry.baidubce.com/rudder/paddlehelix/helixfold_relax:2.0-",
	models.TaskTypeHelixFoldAA:       "xxx",
}

type reqStruct struct {
	TaskId       string `json:"task_id"`
	Framework    string `json:"framework"`
	CodePath     string `json:"code_path"`
	StartCmd     string `json:"start_cmd"`
	OutputPath   string `json:"output_path"`
	TrainsetPath string `json:"trainset_path"`
	CloudId      string `json:"cloud_id"`
	VmType       int64  `json:"vm_type"`
	TrainerCount int64  `json:"trainer_count"`
	Language     string `json:"language"`
	UserTimeout  int64  `json:"user_timeout"`
	Priority     int64  `json:"priority"`
	TestsetPath  string `json:"testset_path"`
	Image        string `json:"image"`
	ExtraParams  string `json:"extra_params"`
}

// 运行方式
const (
	RunModeTrain    = "train"
	RunModeInfer    = "infer"
	RunModePretreat = "preprocess"
	RunModeParse    = "parse"
)

var RunModeMap = map[int]string{
	models.FuncTypeTrain:    RunModeTrain,
	models.FuncTypeForecast: RunModeInfer,
}

// 训练类型
const (
	TrainTypeClassify   = "Classification"
	TrainTypeRegression = "Regression"
)

var TrainTypeMap = map[string]string{
	models.TrainTypeCla: TrainTypeClassify,
	models.TrainTypeReg: TrainTypeRegression,
}

// 基于方式
const (
	BasedTypeTarget = "target"
	BasedTypeLigand = "ligand"
)

var BasedTypeMap = map[string]string{
	models.BasedTypeTarget: BasedTypeTarget,
	models.BasedTypeLigand: BasedTypeLigand,
}

// 默认product
const ProductDefault = 3

// 以下是调度返回数据声明
// 训练状态(返回结果)
const (
	TrainStatusSucc   = 0
	TrainStatusFailed = 1
	TrainStatusCancel = 2
)

var TaskStatusMap = map[int]int{
	TrainStatusSucc:   models.TaskStatusSucc,
	TrainStatusFailed: models.TaskStatusFailed,
}

// Server 数据响应体
const RetCodeSucc = "RET_OK"
const RetCodeFail = "RET_Fail"

type ServerResp struct {
	RetCode string         `json:"ret_code"`
	Results []ServerResult `json:"results"`
}
type ServerResult struct {
	OriTaskId string `json:"ori_task_id"`
	TaskId    int64  `json:"task_id"`
	OrderID   string `json:"order_id"`
}

// 调度 mq 消息结构体
type MqResp struct {
	TaskId          int64  `json:"task_id"`
	RudderTaskId    int64  `json:"rudder_task_id"`
	FileUrl         string `json:"file_url"`
	TrainStatus     int64  `json:"status"`
	ErrorCode       int64  `json:"error_code"`
	LogPath         string `json:"log_path"`
	DownloadUrl     string `json:"download_url"`
	ChargeStartTime int64  `json:"charge_start_time"`
	ChargeEndTime   int64  `json:"charge_end_time"`
	FinishTime      int64  `json:"finish_time"`
	Resource        string `json:"resource_type"`
	JobFailReason   string `json:"job_fail_reason"`
}

// check protein type
func checkProteinTaskType(taskType int) bool {
	if taskType == models.TaskTypeProtein || taskType == models.TaskTypeProteinComplex {
		return true
	}

	return false
}

type KillTaskErrResp struct {
	RequestID int64  `json:"requestId"`
	Code      string `json:"code"`
	Message   string `json:"message"`
}

type KillTaskResp struct {
	TaskID string `json:"taskId"`
}

// KillTask 取消任务
func KillTask(ctx context.Context, task models.Task) error {
	// CHPC调度
	if _, exist := models.CHPCTask[int(task.Type)]; exist {
		jobID := defaultTaskPrefix + RunModeInfer + "_" + strconv.Itoa(int(task.ID))
		uri := fmt.Sprintf("/v1/helix/job/%s", jobID)
		req := &bcehttp.Request{}
		req.SetParam("action", "cancel")
		chpc.AssembleStsRequest(ctx, req, ctxutils.GetIAMUserDomainID(ctx),
			"PUT", chpc.NewCHPClient().Endpoint, uri)
		resp, err := bcehttp.Execute(req)
		if err != nil {
			helpers.LogError(ctx, err)
			go helpers.HelixNotice(ctx, "--- cancel task error ---"+err.Error())
			return err
		}
		defer resp.Body().Close()
		body, err := ioutil.ReadAll(resp.Body())
		if err != nil {
			helpers.LogError(ctx, err)
			go helpers.HelixNotice(ctx, "--- cancel task error ---"+err.Error())
			return err
		}
		if resp.StatusCode() != 200 {
			killTaskErrResp := KillTaskErrResp{}
			_ = json.Unmarshal(body, &killTaskErrResp)
			return errors.New("task cancel fail, " + killTaskErrResp.Message)
		}
		killTaskResp := KillTaskResp{}
		_ = json.Unmarshal(body, &killTaskResp)
		return nil
	}

	// 中台调度
	if task.ServerTaskId <= 0 {
		return nil
	}
	message := map[string]any{
		"task_id": strconv.Itoa(int(task.ServerTaskId)),
		"product": ProductDefault,
	}
	_, err := callSched(ctx, message, "/AIPipelineService/train_task_kill")
	return err
}

// 提交预处理任务
func SubmitPretreat(ctx context.Context, pretreat models.Pretreat) (ServerResult, error) {
	var resp ServerResult
	var pretreatConf models.PretreatConf
	err := json.Unmarshal([]byte(pretreat.Config), &pretreatConf)
	if err != nil {
		return resp, err
	}

	// dataset
	extraParams := make(map[string]any)
	extraParams["task_type"] = TaskTypeMap[int(pretreat.Type)]
	extraParams["run_mode"] = RunModePretreat
	extraParams["data_type"] = "file"
	extraParams["file_url"] = pretreatConf.FileUrl
	if pretreat.Type == uint64(models.TaskTypeKYKT) {
		extraParams["run_mode"] = RunModeParse
	}
	if len(pretreatConf.Serial) > 0 {
		extraParams["data_type"] = "string"

		tmp := strings.Replace(pretreatConf.Serial, "\r", "", -1)
		tmp = strings.Replace(tmp, "\\", "\\\\", -1)
		tmp = strings.Replace(tmp, "\t", "\\t", -1)
		extraParams["smiles"] = strings.Replace(tmp, "\n", "\\n", -1)
	}
	configByte, _ := json.Marshal(extraParams)

	// 获取bos配置
	bosOutputPath, err := helpers.GetConfField(env.ConfDir()+"/bos.toml", "ServerPretreatPath")
	if err != nil {
		return resp, err
	}

	runMode := RunModePretreat
	item := reqStruct{
		TaskId:       defaultTaskPrefix + runMode + "_" + strconv.Itoa(int(pretreat.ID)),
		Framework:    defaultFramework,
		CodePath:     defaultCodePath,
		StartCmd:     defaultStartCmd,
		OutputPath:   bosOutputPath + strconv.Itoa(int(pretreat.ID)),
		TrainsetPath: defaultTrainsetPath,
		CloudId:      defaultCloudId,
		VmType:       7,
		TrainerCount: defaultTrainerCount,
		Language:     defaultLanguage,
		UserTimeout:  defaultUserTimeout,
		TestsetPath:  defaultTestsetPath,
		Image:        "registry.baidubce.com/rudder/paddlehelix/virtual_screening:2.0-" + ImageEnvMap[env.RunMode()],
		ExtraParams:  string(configByte),
	}
	if pretreat.Type == uint64(models.TaskTypeKYKT) {
		item.Image = ImageMap[int(pretreat.Type)] + ImageEnvMap[env.RunMode()]
	}
	message := map[string]any{
		"items": []any{item},
	}

	serResp, err := callSched(ctx, message, "/AIPipelineService/custom_job_task_submit")
	if err != nil {
		return resp, err
	}
	if len(serResp.Results) == 0 {
		return resp, errors.New("result data null")
	}

	return serResp.Results[0], nil
}

// 提交任务到调度
func SubmitTaskNew(ctx context.Context, task models.Task) (ServerResult, error) {
	var resp ServerResult
	var taskConf models.TaskConfig

	err := json.Unmarshal([]byte(task.Config), &taskConf)
	if err != nil {
		return resp, err
	}

	// extraParams
	extraParams := make(map[string]any)
	extraParams["task_type"] = TaskTypeMap[int(task.Type)]
	extraParams["run_mode"] = RunModeMap[int(task.FuncType)]
	extraParams["is_cameo"] = taskConf.IsCameo
	if len(taskConf.FileURL) > 0 {
		extraParams["data_type"] = "file"
		extraParams["file_url"] = taskConf.FileURL
	} else {
		extraParams["data_type"] = "string"
		extraParams["smiles"] = taskConf.Serial
		if task.Type == uint64(models.TaskTypeMolActivity) {
			extraParams["protein"] = taskConf.Protein
		}
		if task.Type == uint64(models.TaskTypeDoubleDrug) {
			extraParams["cell_line"] = taskConf.CellLine
			extraParams["tissue"] = taskConf.Tissues
		}
		if task.Type == uint64(models.TaskTypeAntibody) ||
			task.Type == uint64(models.TaskTypeNewCrown) ||
			task.Type == uint64(models.TaskTypeKYKT) {
			extraParams["heavy_serial"] = taskConf.HeavySerial
			extraParams["light_serial"] = taskConf.LightSerial
			extraParams["antigen_serial"] = taskConf.AntigenSerial
		}
	}

	if len(taskConf.ModelPath) > 0 {
		extraParams["model_path"] = taskConf.ModelPath
	}
	if task.Type == uint64(models.TaskTypeLinearFold) {
		extraParams["beam_size"] = taskConf.BeamSize
		extraParams["constraints"] = taskConf.Constraint
		extraParams["zuker_score"] = taskConf.Score
	}
	if task.Type == uint64(models.TaskTypeUTR) {
		extraParams["target_len"] = taskConf.TargetLen
		extraParams["given_refer_5utr"] = taskConf.GivenUtr
		extraParams["5utr_smiles"] = taskConf.UtrSerial
		extraParams["5utr_file_url"] = taskConf.UtrFileUrl
	}
	if task.Type == uint64(models.TaskTypeMolActivity) {
		extraParams["use_bingdingdb"] = taskConf.UseBingdingdb
		extraParams["use_chembl"] = taskConf.UseChembl
	}
	if task.Type == uint64(models.TaskTypeSelfAdmet) {
		extraParams["model_list"] = taskConf.ModelList
		if len(taskConf.TrainType) > 0 {
			extraParams["train_type"] = TrainTypeMap[taskConf.TrainType]
		}
	}
	if task.Type == uint64(models.TaskTypeMolFormation) {
		extraParams["based_type"] = BasedTypeMap[taskConf.BasedType]
		extraParams["model_type"] = "base"
	}
	if task.Type == uint64(models.TaskTypeMolFormPath) {
		extraParams["based_type"] = BasedTypeMap[taskConf.BasedType]
		extraParams["model_type"] = "route"
	}
	if task.Type == uint64(models.TaskTypeProtein) {
		extraParams["ensembling"] = taskConf.Ensembling
		extraParams["relaxation"] = taskConf.Relaxation
		extraParams["fold2"] = taskConf.Fold2
		if len(taskConf.PdbURL) > 0 {
			lastIndex := strings.LastIndex(taskConf.PdbURL, "/")
			extraParams["pdb_path"] = taskConf.PdbURL[0 : lastIndex+1]
			extraParams["pdb_name"] = taskConf.PdbName
		}
	}
	if task.Type == uint64(models.TaskTypeProteinFunc) {
		extraParams["pdb_path"] = taskConf.PdbURL
		extraParams["pdb_name"] = taskConf.PdbName
	}
	if task.Type == uint64(models.TaskTypeHelixVSSyn) {
		extraParams["index_list"] = taskConf.IndexList
	}
	if task.Type == uint64(models.TaskTypeVirtualFilter) ||
		task.Type == uint64(models.TaskTypeVirtualVS) ||
		task.Type == uint64(models.TaskTypeHelixDock) ||
		task.Type == uint64(models.TaskTypeHelixVSSyn) ||
		task.Type == uint64(models.TaskTypeMolFormPath) ||
		task.Type == uint64(models.TaskTypeMolFormation) {
		extraParams["center_x"] = taskConf.CenterX
		extraParams["center_y"] = taskConf.CenterY
		extraParams["center_z"] = taskConf.CenterZ
		extraParams["size_x"] = taskConf.SizeX
		extraParams["size_y"] = taskConf.SizeY
		extraParams["size_z"] = taskConf.SizeZ

		lastIndex := strings.LastIndex(taskConf.PdbURL, "/")
		extraParams["pdb_path"] = taskConf.PdbURL[0 : lastIndex+1] // 这个key 先保留着吧
		extraParams["pdb_name"] = taskConf.PdbName
		extraParams["pocket_source"] = taskConf.PocketSource

		if len(taskConf.PdbUrl2) > 0 {
			extraParams["center_x2"] = taskConf.CenterX2
			extraParams["center_y2"] = taskConf.CenterY2
			extraParams["center_z2"] = taskConf.CenterZ2
			extraParams["size_x2"] = taskConf.SizeX2
			extraParams["size_y2"] = taskConf.SizeY2
			extraParams["size_z2"] = taskConf.SizeZ2

			lastIndex := strings.LastIndex(taskConf.PdbUrl2, "/")
			extraParams["pdb_path2"] = taskConf.PdbUrl2[0 : lastIndex+1]
			extraParams["pdb_name2"] = taskConf.PdbName2
			extraParams["pocket_source2"] = taskConf.PocketSource2
			extraParams["protein_type2"] = taskConf.ProteinType2
		}
	}
	if task.Type == uint64(models.TaskTypeDoubleDrug) {
		extraParams["drug_a"] = taskConf.DrugA
		extraParams["drug_b"] = taskConf.DrugB
	}
	if task.Type == uint64(models.TaskTypeProteinMutation) {
		var point1List, point2List []string
		_ = json.Unmarshal([]byte(taskConf.Point1), &point1List)
		_ = json.Unmarshal([]byte(taskConf.Point2), &point2List)
		extraParams["protein1"] = taskConf.Protein1
		extraParams["protein2"] = taskConf.Protein2
		extraParams["point1"] = point1List
		extraParams["point2"] = point2List
	}
	if task.Type == uint64(models.TaskTypeMolActStruct) {
		extraParams["mol_url"] = taskConf.MolURL
		extraParams["protein_url"] = taskConf.ProteinURL
	}
	if task.Type == uint64(models.TaskTypeExactDrug) {
		var drugList []string
		_ = json.Unmarshal([]byte(taskConf.DrugList), &drugList)
		extraParams["mol_url"] = taskConf.MolURL
		extraParams["drug_list"] = drugList
		extraParams["sample"] = taskConf.Sample
		extraParams["sample_url"] = taskConf.SampleUrl
	}
	if task.Type == uint64(models.TaskTypeMMGBSA) {
		extraParams["mol_url"] = taskConf.MolURL
		extraParams["mol_name"] = taskConf.MolName
		extraParams["protein_url"] = taskConf.ProteinURL
		extraParams["gb_model"] = taskConf.GbModel
		extraParams["simulation_len"] = taskConf.SimulationLen
		extraParams["epsilon"] = taskConf.Epsilon
	}
	if task.Type == uint64(models.TaskTypeHelixFoldAA) {
		extraParams["random_seed"] = taskConf.RandomSeed
		extraParams["entities"] = taskConf.Entities
	}
	if task.Type == uint64(models.TaskTypeProteinRelaxation) {
		extraParams["protein_url"] = taskConf.ProteinURL
	}
	if task.Type == uint64(models.TaskTypeVirtualVS) {
		extraParams["mol_num"] = taskConf.MolNum
		extraParams["filter_str"] = taskConf.FilterStr
		extraParams["filter_lib"] = taskConf.FilterLib
		extraParams["need_init_models"] = true
	}

	for key, val := range extraParams {
		if value, ok := val.(string); ok {
			tmp := strings.Replace(value, "\r", "", -1)
			tmp = strings.Replace(tmp, "\\", "\\\\", -1)
			tmp = strings.Replace(tmp, "\t", "\\t", -1)
			extraParams[key] = strings.Replace(tmp, "\n", "\\n", -1)
		}
	}
	configByte, _ := json.Marshal(extraParams)

	// 获取bos配置
	bosOutputPath, err := helpers.GetConfField(env.ConfDir()+"/bos.toml", "ServerOutputPath")
	if err != nil {
		return resp, err
	}

	lastIndex := strings.LastIndex(taskConf.PdbURL, "/")
	runMode := RunModeMap[int(task.FuncType)]
	item := reqStruct{
		TaskId:       defaultTaskPrefix + runMode + "_" + strconv.Itoa(int(task.ID)),
		Framework:    defaultFramework,
		CodePath:     defaultCodePath,
		StartCmd:     defaultStartCmd,
		OutputPath:   bosOutputPath + strconv.Itoa(int(task.ID)),
		TrainsetPath: taskConf.PdbURL[0 : lastIndex+1],
		CloudId:      defaultCloudId,
		VmType:       VmTypeMap[int(task.Type)],
		TrainerCount: defaultTrainerCount,
		Language:     defaultLanguage,
		UserTimeout:  defaultUserTimeout,
		TestsetPath:  defaultTestsetPath,
		Image:        ImageMap[int(task.Type)] + ImageEnvMap[env.RunMode()],
		ExtraParams:  string(configByte),
	}
	if task.Type == uint64(models.TaskTypeProteinSingle) {
		item.Priority = 1
	}
	if checkProteinTaskType(int(task.Type)) && taskConf.SerialLen > DokaSerialLen {
		item.VmType = DokaVmType
	}
	// helix-vs 的Framework
	if task.Type == uint64(models.TaskTypeVirtualVS) {
		item.Framework = helixVSFramework
		item.StartCmd = helixVSStartCmd
	}

	message := map[string]any{
		"items": []any{item},
	}

	// 提交任务
	serResp, err := callSched(ctx, message, "/AIPipelineService/custom_job_task_submit")

	// 调度说heliXVS HOST PORT PATH 都不变,那先注释掉
	//if task.Type == uint64(models.TaskTypeVirtualVS) {
	//	// TODO 1:path 等调度提供后修改, 2:conf配置 ip port 同步修改
	//	serResp, err = callHelixVS(ctx, message, "/AIPipelineService/custom_job_task_submit")
	//}
	if err != nil {
		return resp, err
	}
	if len(serResp.Results) == 0 {
		return resp, errors.New("result data null")
	}

	return serResp.Results[0], nil
}

// call 调度系统
func callSched(ctx context.Context, message map[string]any, reqPath string) (ServerResp, error) {
	messageJson, _ := json.Marshal(message)
	resource.LoggerService.Notice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson)+"---service:uri---scheduler:"+reqPath)

	ralReq := &ghttp.RalPostRequest{
		PostData: message,
		Encoder:  codec.JSONEncoder,
	}
	ralReq.Path = reqPath
	ralReq.Header = map[string][]string{
		"Log-Id":       {},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	var resp ServerResp
	ralResp := &ghttp.RalResponse{
		Data:    &ServerResp{RetCode: "Failed"},
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, "scheduler", ralReq, ralResp)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return resp, err
	}

	// 写数据日志
	respJson, _ := json.Marshal(ralResp.Data)
	resource.LoggerService.Notice(ctx, string(respJson))
	go helpers.HelixNotice(ctx, string(respJson))

	// 返回数据处理
	respData := ralResp.Data.(*ServerResp)
	if respData.RetCode != RetCodeSucc {
		return *respData, errors.New("ret_code:" + respData.RetCode)
	}
	return *respData, nil
}

// callHelixVS helixVS任务
func callHelixVS(ctx context.Context, message map[string]any, reqPath string) (ServerResp, error) {
	messageJson, _ := json.Marshal(message)
	resource.LoggerService.Notice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson)+"---service:uri---helixvs:"+reqPath)

	ralReq := &ghttp.RalPostRequest{
		PostData: message,
		Encoder:  codec.JSONEncoder,
	}
	ralReq.Path = reqPath
	ralReq.Header = map[string][]string{
		"Log-Id":       {},
		"Content-Type": {"application/json;charset=UTF-8"},
	}

	var resp ServerResp
	ralResp := &ghttp.RalResponse{
		Data:    &ServerResp{RetCode: "Failed"},
		Decoder: codec.JSONDecoder,
	}
	err := ral.RAL(ctx, "helixvs", ralReq, ralResp)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return resp, err
	}

	// 写数据日志
	respJson, _ := json.Marshal(ralResp.Data)
	resource.LoggerService.Notice(ctx, string(respJson))
	go helpers.HelixNotice(ctx, string(respJson))

	// 返回数据处理
	respData := ralResp.Data.(*ServerResp)
	if respData.RetCode != RetCodeSucc {
		return *respData, errors.New("ret_code:" + respData.RetCode)
	}
	return *respData, nil
}

// JobRequest 表示作业提交请求的结构体
type JobRequest struct {
	JobName            string            `json:"jobName"`            // 作业名称
	JobCmd             string            `json:"jobCmd"`             // 执行作业的命令
	Queue              string            `json:"queue"`              // 作业所在的队列
	NHosts             int               `json:"nhosts"`             // 作业所需的主机数量
	LimitTimeInMinutes int               `json:"limitTimeInMinutes"` // 作业的时间限制（秒）
	PostCmd            string            `json:"postCmd"`            // 作业完成后的后续命令
	StdoutPath         string            `json:"stdoutPath"`         // 标准输出文件路径
	StderrPath         string            `json:"stderrPath"`         // 标准错误文件路径
	EnvVars            map[string]string `json:"envVars"`            // 环境变量
	BosFilePath        string            `json:"bosFilePath"`        // 作业所需的 BOS 文件路径
	DecompressCmd      string            `json:"decompressCmd"`      // 解压缩命令
	JobProduct         string            `json:"jobProduct"`
}

// JobResponse 表示作业提交请求的结构体
type JobResponse struct {
	RequestId string `json:"requestId"`
	JobId     string `json:"taskId"`
	OrderID   string `json:"orderId"`
}

// SubmitJob 提交任务到调度
func SubmitJob(ctx context.Context, task models.Task) (ServerResult, error) {
	var (
		resp     ServerResult
		taskConf models.TaskConfig
	)

	// parse task param
	var (
		jobProduct string
		jobCmd     string
		queue      string
	)
	err := json.Unmarshal([]byte(task.Config), &taskConf)
	if err != nil {
		return resp, err
	}
	modelInputJson := make(map[string]any)
	message := make(map[string]any)
	switch task.Type {
	case uint64(models.TaskTypeHelixFoldAA), uint64(models.TaskTypeHelixFold3S1):
		modelInputJson["random_seed"] = taskConf.RandomSeed
		modelInputJson["recycle"] = taskConf.Recycle
		modelInputJson["ensemble"] = taskConf.Ensemble
		modelInputJson["model_type"] = taskConf.ModelType
		modelInputJson["entities"] = taskConf.Entities
		if taskConf.Constraints != nil {
			modelInputJson["constraint"] = taskConf.Constraints
		}
		if taskConf.RefStructures != nil {
			modelInputJson["ref_structures"] = taskConf.RefStructures
		}
		if taskConf.S1SampleConstraint != nil {
			modelInputJson["s1_sample_constraint"] = taskConf.S1SampleConstraint
		}
		modelInputJson["job_name"] = strconv.FormatUint(task.ID, 10)
		jobProduct = "helixfold3"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/helixfold/chpc_start.sh"
		message["n_tokens"] = task.NTokens
		message["billing_unit_count"] = ctxutils.GetBillingUnitCount(ctx)
	case uint64(models.TaskTypeVirtualVS):
		modelInputJson["pdb_path"] = taskConf.PdbURL
		modelInputJson["pdb_name"] = taskConf.PdbName
		modelInputJson["mol_num"] = taskConf.MolNum
		modelInputJson["filter_str"] = taskConf.FilterStr
		modelInputJson["input_ligand_library"] = taskConf.FilterLib
		modelInputJson["center_x"] = taskConf.CenterX
		modelInputJson["center_y"] = taskConf.CenterY
		modelInputJson["center_z"] = taskConf.CenterZ
		modelInputJson["size_x"] = taskConf.SizeX
		modelInputJson["size_y"] = taskConf.SizeY
		modelInputJson["size_z"] = taskConf.SizeZ
		jobProduct = "helixvs"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/helixvs/chpc_start.sh"
		message["billing_unit_count"] = taskConf.BillingUnitCount
	case uint64(models.TaskTypeHF3Agab):
		modelInputJson["task_type"] = taskConf.TaskType
		modelInputJson["run_mode"] = taskConf.RunMode
		modelInputJson["smiles"] = taskConf.Smiles
		modelInputJson["data_type"] = taskConf.DataType
		jobProduct = "hf3agab"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/hf3agab/chpc_start.sh"
		message["n_tokens"] = task.NTokens
		message["billing_unit_count"] = ctxutils.GetBillingUnitCount(ctx)
	case uint64(models.TaskTypeMiniProteinDesign):
		modelInputJson["target_sequence"] = taskConf.TargetSequence
		modelInputJson["binder_min_length"] = taskConf.BinderMinLength
		modelInputJson["binder_max_length"] = taskConf.BinderMaxLength
		modelInputJson["design_mode"] = taskConf.DesignMode
		modelInputJson["job_name"] = strconv.FormatUint(task.ID, 10)
		jobProduct = "miniprotein_design"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/miniprotein_design/chpc_start.sh"
		message["n_tokens"] = task.NTokens
		message["billing_unit_count"] = ctxutils.GetBillingUnitCount(ctx)
	case uint64(models.TaskTypeHelixVSSyn):
		modelInputJson["pdb_path"] = taskConf.PdbURL
		modelInputJson["pdb_name"] = taskConf.PdbName
		modelInputJson["design_mol_num"] = taskConf.DesignMolNum
		modelInputJson["filter_str"] = taskConf.FilterStr
		modelInputJson["input_ligand_library"] = taskConf.FilterLib
		modelInputJson["center_x"] = taskConf.CenterX
		modelInputJson["center_y"] = taskConf.CenterY
		modelInputJson["center_z"] = taskConf.CenterZ
		modelInputJson["size_x"] = taskConf.SizeX
		modelInputJson["size_y"] = taskConf.SizeY
		modelInputJson["size_z"] = taskConf.SizeZ
		modelInputJson["design"] = true
		modelInputJson["reference_ligs"] = taskConf.Serial
		modelInputJson["mol_num"] = taskConf.MolNum
		var reference_sites []string
		for _, index := range taskConf.IndexList {
			reference_sites = append(reference_sites, strconv.Itoa(int(index)))
		}
		modelInputJson["reference_sites"] = strings.Join(reference_sites, ",")
		jobProduct = "helixvs_syn"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/helixvs/chpc_start.sh"
		message["billing_unit_count"] = taskConf.BillingUnitCount
	case uint64(models.TaskTypeAntibodyDesign):
		modelInputJson["design_num"] = taskConf.DesignNum
		modelInputJson["diverse"] = taskConf.Diverse
		modelInputJson["reference"] = taskConf.Reference
		modelInputJson["design_mode"] = taskConf.DesignMode
		modelInputJson["design_chains"] = taskConf.DesignChains
		modelInputJson["job_name"] = task.Name
		jobProduct = "antibody_design"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/antibody_design/chpc_start.sh"
		message["n_tokens"] = task.NTokens
		message["billing_unit_count"] = taskConf.BillingUnitCount
	case uint64(models.TaskTypeLinearDesign):
		modelInputJson["design_mode"] = taskConf.DesignMode
		modelInputJson["name"] = taskConf.Name
		modelInputJson["sequence"] = taskConf.Sequence
		modelInputJson["sequence_file_url"] = taskConf.SequenceFileURL
		modelInputJson["sequence_type"] = taskConf.SequenceType
		modelInputJson["sequence_5utr"] = taskConf.Sequence5UTR
		modelInputJson["sequence_3utr"] = taskConf.Sequence3UTR
		modelInputJson["param_cds"] = taskConf.ParamCDS
		modelInputJson["param_5utr"] = taskConf.Param5UTR
		jobProduct = "linear_design"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/linear_design/chpc_start.sh"
		message["billing_unit_count"] = taskConf.BillingUnitCount
	case uint64(models.TaskTypeLinearFold):
		modelInputJson["name"] = taskConf.Name
		modelInputJson["sequence"] = taskConf.Sequence
		modelInputJson["sequence_file_url"] = taskConf.SequenceFileURL
		modelInputJson["need_fold"] = taskConf.NeedFold
		modelInputJson["need_partition"] = taskConf.NeedPartition
		modelInputJson["fold_config"] = taskConf.FoldConfig
		jobProduct = "linear_fold"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/linear_fold/chpc_start.sh"
		message["billing_unit_count"] = taskConf.BillingUnitCount
	case uint64(models.TaskTypeLinearPartition):
		modelInputJson["name"] = taskConf.Name
		modelInputJson["sequence"] = taskConf.Sequence
		modelInputJson["sequence_file_url"] = taskConf.SequenceFileURL
		modelInputJson["need_fold"] = taskConf.NeedFold
		modelInputJson["need_partition"] = taskConf.NeedPartition
		modelInputJson["partition_config"] = taskConf.PartitionConfig
		jobProduct = "linear_partition"
		jobCmd = "/plugin/chpc/slurm/22.05.9/0.1.0/applications/linear_partition/chpc_start.sh"
		message["billing_unit_count"] = taskConf.BillingUnitCount
	}

	// assemble request
	runMode := RunModeMap[int(task.FuncType)]
	item := reqStruct{
		TaskId: defaultTaskPrefix + runMode + "_" + strconv.Itoa(int(task.ID)),
	}
	message["items"] = []any{item}
	message["model_input_json"] = modelInputJson
	message["user_type"] = ctxutils.GetUserInfo(ctx).Type
	message["iam_account_id"] = ctxutils.GetIAMUserDomainID(ctx)
	message["env"] = env.RunMode()
	message["github_real_id"] = ctxutils.GetGitHubUserRealId(ctx)
	messageJson, _ := json.Marshal(message)
	jobRequest := JobRequest{
		JobName:            strconv.FormatUint(task.ID, 10),
		JobCmd:             jobCmd,
		Queue:              queue,
		NHosts:             1,
		LimitTimeInMinutes: 10080,
		PostCmd:            "",
		StdoutPath:         "",
		StderrPath:         "",
		EnvVars: map[string]string{
			"output_path":  "/home/<USER>/output",
			"extra_params": string(messageJson),
		},
		BosFilePath:   "",
		DecompressCmd: "",
		JobProduct:    jobProduct,
	}

	// submit task really
	chpcCli := chpc.NewCHPClient()
	useChpcAcc := false
	if message["billing_unit_count"].(float64) == 0.0 {
		useChpcAcc = true
	}
	serResp, err := SubmitJobReal(ctx, task.ID, jobRequest, chpcCli, useChpcAcc)
	if err != nil {
		return resp, err
	}

	// check task submit result
	if len(serResp.Results) == 0 {
		return resp, errors.New("result data null")
	}

	return serResp.Results[0], nil
}

// SubmitJobReal 提交任务，将任务提交到真实的服务器上执行
//
// 参数：
//
//	ctx (context.Context) - 上下文信息，包含请求的状态和其他可选信息
//	taskId (uint64) - 任务ID，用于标记任务在系统中的唯一性
//	message (JobRequest) - 任务请求结构体，包含了任务的相关信息，如命令、参数等
//	uri (string) - 服务器URI，用于标记任务提交的目标服务器
//
// 返回值：
//
//	(ServerResp, error) - 包含两个元素，第一个是服务器响应结构体，第二个是错误信息（如果有）
func SubmitJobReal(ctx context.Context, taskId uint64, message JobRequest, chpcCli *chpc.CHPClient,
	useChpcAcc bool) (ServerResp, error) {
	messageJson, _ := json.Marshal(message)
	resource.LoggerService.Notice(ctx, string(messageJson))
	go helpers.HelixNotice(ctx, string(messageJson)+"---service:uri---scheduler:"+chpcCli.JobSubmitPath)
	var resp ServerResp

	// 构造任务提交请求
	req := &bcehttp.Request{}
	assembleSubmitTaskRequest(ctx, req, chpcCli, messageJson, useChpcAcc)

	// 发送任务提交请求
	response, err := bcehttp.Execute(req)
	if err != nil {
		helpers.LogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return resp, err
	}
	defer response.Body().Close()

	// 读取请求响应
	body, err := ioutil.ReadAll(response.Body())
	if err != nil {
		helpers.LogError(ctx, err)
		return ServerResp{}, err
	}
	jobResponse := JobResponse{}
	_ = json.Unmarshal(body, &jobResponse)

	// 写数据日志
	resource.LoggerService.Notice(ctx, string(body))
	go helpers.HelixNotice(ctx, string(body))

	// 返回数据处理
	if response.StatusCode() != 200 || jobResponse.JobId == "" {
		return resp, errors.New("ret_code:" + RetCodeFail)
	}
	resp.RetCode = RetCodeSucc
	resp.Results = []ServerResult{{
		TaskId:    int64(taskId),
		OriTaskId: jobResponse.JobId,
		OrderID:   jobResponse.OrderID,
	}}
	return resp, nil
}

// 装配任务提交请求
func assembleSubmitTaskRequest(ctx context.Context, req *bcehttp.Request, chpcCli *chpc.CHPClient,
	message []byte, useChpcAcc bool) {
	var (
		ak           = chpc.NewCHPClient().AK
		sk           = chpc.NewCHPClient().SK
		sessionToken = ""
	)

	// 如果是非内部用户的IAM登录用户 且已开通CHPC服务 则根据IAM帐号ID获取AK、SK
	iamUserDomainID := ctxutils.GetIAMUserDomainID(ctx)
	user := ctxutils.GetUserInfo(ctx)
	open := chpc.IsOpenCHPCService(iamUserDomainID)
	if user.Type != int64(models.UserTypeInside) && len(iamUserDomainID) > 0 && open && !useChpcAcc {
		credential, err := chpc.ParseCredentialFromUserID(iamUserDomainID, ctxutils.GetIAMUserID(ctx))
		if err != nil {
			helpers.LogError(ctx, fmt.Errorf("during assemble sts request, parse credential of account %s failed, "+
				"err is %v", iamUserDomainID, err))
		} else {
			ak, sk, sessionToken = credential.AccessKeyID, credential.SecretAccessKey, credential.SessionToken
		}
	}

	// iam 添加sts auth认证
	clientConf := bcehttp.ClientConfig{}
	bcehttp.InitClient(clientConf)
	req.SetProtocol("http")
	req.SetMethod("POST")
	req.SetHost(chpcCli.Endpoint)
	req.SetUri(chpcCli.JobSubmitPath)
	req.SetHeader("Host", chpcCli.Endpoint)
	req.SetHeader("Content-Type", "application/json")
	if len(sessionToken) > 0 {
		req.SetHeader("x-bce-security-token", sessionToken)
	}
	reader := ioutil.NopCloser(bytes.NewReader(message))
	req.SetBody(reader)
	req.SetLength(int64(len(message)))
	chpc.GenerateStsAuthHeader(req, ak, sk)
}
