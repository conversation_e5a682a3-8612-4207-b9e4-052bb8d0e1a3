"""
Code for file verification
"""

import os
import sys
import re
from textwrap import indent 
import anarci
import json
from collections import defaultdict
import argparse
from Bio.PDB import PDBParser
from Bio.PDB.PDBIO import PDBIO
from Bio.SeqIO.PdbIO import PdbSeqresIterator
from Bio.PDB.MMCIF2Dict import MMCIF2Dict
import numpy as np
from Bio.PDB import M<PERSON>IFParser
from Bio import pairwise2  
from utils import extract_seqres_from_CIF, extract_seq_from_cif_coords

os.environ["PATH"] += os.pathsep + "/home/<USER>/service/output/checker/bin"

_cdr1 = [str(p) for p in range(25, 41)]
_cdr2 = [str(p) for p in range(54, 68)]
_cdr3 = [str(p) for p in range(103, 120)]


def is_sequential(id_list):
    """to tell if index is sequentail"""
    idx = list(map(int, id_list))
    start, end = min(idx), max(idx)

    return end - start + 1 - len(idx)


def get_paired_light_chain(mmcif_file, heavy_chain_id):
    """get paired chain"""
    # Parse the mmCIF file
    parser = MMCIFParser()
    structure = parser.get_structure('complex', mmcif_file)
    
    # Get all chains
    chains = list(structure.get_chains())

    mmcif_dict = MMCIF2Dict(mmcif_file)
    auth_ids = mmcif_dict.get("_entity_poly.pdbx_strand_id", [])
    
    valid_chains = [c[0] for c in auth_ids]
    chains = [ch for ch in chains if ch in auth_ids]
    # Find the heavy chain of interest
    heavy_chain = None
    for chain in chains:
        if chain.id == heavy_chain_id:
            heavy_chain = chain
            break
    
    if heavy_chain is None:
        return f"Chain {heavy_chain_id} not found"
    
    # Get the center of mass of the heavy chain
    heavy_atoms = list(heavy_chain.get_atoms())
    heavy_com = np.mean([atom.coord for atom in heavy_atoms], axis=0)
    
    # Find distances between heavy chain and all other chains
    chain_distances = {}
    for chain in chains:
        if chain.id != heavy_chain_id:
            chain_atoms = list(chain.get_atoms())
            chain_com = np.mean([atom.coord for atom in chain_atoms], axis=0)
            distance = np.linalg.norm(heavy_com - chain_com)
            chain_distances[chain.id] = distance
    
    # Sort chains by distance to heavy chain
    sorted_chains = sorted(chain_distances.items(), key=lambda x: x[1])
    
    # Determine if closest chain is a light chain
    # This requires examining the residues to identify light chain characteristics
    closest_chain_id = sorted_chains[0][0]
    closest_chain = None
    for chain in chains:
        if chain.id == closest_chain_id:
            closest_chain = chain
            break
    
    seqs, _ = extract_seq_from_cif_coords(mmcif_file)
    infom = anarci.run_anarci(seqs.get(closest_chain.id))

    if infom[1][0]: #antibody chain
        pass
    else:
        closest_chain_id = None

    return closest_chain_id


def filter_non_std_res(seq):
    """ ."""
    non_std_res = re.findall("\(.+\)", seq)
    for ns in non_std_res:
        seq = seq.replace(ns, "")
    
    return seq


def verif(jsonfile):
    """check"""

    # coords seq and full seq extraction
    input_json = json.load(open(jsonfile))
    input_struct = input_json.get('reference')
    formatt = input_struct.split(".")[-1]

    #if formatt == "cif":
    struct_seqs, seq_idx = extract_seq_from_cif_coords(input_struct) #coords seq
    
    full_chains = extract_seqres_from_CIF(input_struct)

    if len(full_chains) < 2:
        print("该文件有效链的数目<2！请输入有效的构象文件")
        exit(1)

    full_imgt2idx = dict()
    region_info = dict()
    # missing_region = dict()
    heavy_exist = False
    heavy_id, light_id = [], []
    antigen_exist = False

    #find the exact Fv and other regions
    for chain in full_chains:
        
        infos = anarci.run_anarci(full_chains[chain])
        whole_seq = full_chains[chain]
        if infos[1][0] and infos[2][0][0]['chain_type'] == 'H':
            
            heavy_exist = True
            imgt = infos[1][0][0][0]
            d1 =  [(str(a[0]), b) for a, b in imgt]
            Fv = ''.join([b for a, b in imgt]).replace('-', '')
            Fv_end_idx = full_chains[chain].index(Fv) + len(Fv)
            Fc = full_chains[chain][Fv_end_idx:]
            full_imgt2idx[chain] = d1
            # add CDR region index
            cdr1_seq = ''.join([b for a, b in d1 if a in _cdr1]).replace('-', '')
            cdr1_se = (whole_seq.index(cdr1_seq), whole_seq.index(cdr1_seq) + len(cdr1_seq))

            cdr2_seq = ''.join([b for a, b in d1 if a in _cdr2]).replace('-', '')
            cdr2_se = (whole_seq.index(cdr2_seq), whole_seq.index(cdr2_seq) + len(cdr2_seq))

            cdr3_seq = ''.join([b for a, b in d1 if a in _cdr3]).replace('-', '')
            cdr3_se = (whole_seq.index(cdr3_seq), whole_seq.index(cdr3_seq) + len(cdr3_seq))


            region_info[chain] = {"chain_type": "Hchain",
                                  "Fv_end_idx": Fv_end_idx,
                                  "cdr1_idx": cdr1_se,
                                  "cdr2_idx": cdr2_se,
                                  "cdr3_idx": cdr3_se}

        elif infos[1][0]:
            imgt = infos[1][0][0][0]
            d1 =  [(str(a[0]), b) for a, b in imgt] 
            Fv = ''.join([b for a, b in imgt]).replace('-', '')
            Fv_end_idx = full_chains[chain].index(Fv) + len(Fv)
            Fc = full_chains[chain][Fv_end_idx:]
            full_imgt2idx[chain] = d1
            cdr1_seq = ''.join([b for a, b in d1 if a in _cdr1]).replace('-', '')
            cdr1_se = (whole_seq.index(cdr1_seq), whole_seq.index(cdr1_seq) + len(cdr1_seq))

            cdr2_seq = ''.join([b for a, b in d1 if a in _cdr2]).replace('-', '')
            cdr2_se = (whole_seq.index(cdr2_seq), whole_seq.index(cdr2_seq) + len(cdr2_seq))

            cdr3_seq = ''.join([b for a, b in d1 if a in _cdr3]).replace('-', '')
            cdr3_se = (whole_seq.index(cdr3_seq), whole_seq.index(cdr3_seq) + len(cdr3_seq))


            region_info[chain] = {"chain_type": "Lchain",
                                  "Fv_end_idx": Fv_end_idx,
                                  "cdr1_idx": cdr1_se,
                                  "cdr2_idx": cdr2_se,
                                  "cdr3_idx": cdr3_se}

        else:
            region_info[chain] = {"chain_type": "Antigen",
                                  "all": whole_seq}
            antigen_exist = True

    if not heavy_exist:
        print("您输入的文件中没有抗体重链，请重新输入。")
        exit(1)
    
    if  not antigen_exist:
        print("您输入的文件中没有抗原，请重新输入。")
        exit(1)
    
    # align the seqs from two above pair by pair
    chain_info_list = dict() #list()
    for chain in full_chains:
        
        chain_parsed = {"chian_ID": chain}
        
        full_seq, coord_seq = full_chains.get(chain), struct_seqs.get(chain)
        chain_parsed["length"] = len(coord_seq)
        #using Needleman-Wunsch
        alignments = pairwise2.align.globalms(full_seq, 
                                             coord_seq,
                                             match=1,    # Match score  
                                            mismatch=-1, # Mismatch penalty  
                                            open=-2,      # Gap open penalty (higher = fewer gaps)  
                                            extend=-1)
        ref, aln = alignments[0].seqA, alignments[0].seqB
        
        if len(ref) != len(full_seq):
            print("结构解析失败。请重新选择！")
            exit(1)

        # check function and find the nearest paired chain
        if region_info.get(chain).get("chain_type") == "Hchain":
            chain_parsed["function"] = 'Hchain'
            #if nanobody
            
            paired_chain = get_paired_light_chain(input_struct, chain)
            
            chain_parsed["hl_pair"] = paired_chain
        elif region_info.get(chain).get("chain_type") == "Lchain":
            chain_parsed["function"] = 'Lchain'
            paired_chain = get_paired_light_chain(input_struct, chain)
            
            chain_parsed["hl_pair"] = paired_chain
        else:
            chain_parsed["function"] = 'Antigen'
            chain_parsed["hl_pair"] = "None"

        # check cdr_complete
        if chain_parsed["function"] == "Antigen":
            cdr_complete = False
        else:
            cdr_complete = True
            for pos in ["cdr1_idx", "cdr2_idx", "cdr3_idx"]:
                start, end = region_info[chain].get(pos)
                if len(aln[start: end].replace("-", "")) == 0:
                    cdr_complete = False
        chain_parsed["cdr_complete"] = cdr_complete

        # check the deficiency status, differentiate Fv, Fc, CDR and FR
        # align the mmcif residue index to the seq alignment way
        chain_res_idx = seq_idx.get(chain)
        chain_res_idx_aln = list()
        idx_ptr = 0
        for r in aln:
            if r == "-":
                chain_res_idx_aln.append("-")
            else:
                chain_res_idx_aln.append(chain_res_idx[idx_ptr])
                idx_ptr += 1
        
        #chain_res_idx_non_aln_map = dict(zip(range(len(coord_seq)), coord_seq))
        missing_fv, missing_fc, missing_all = 0, 0, 0
        designable_indices = list()
        length_fill = 0
        for index in range(len(chain_res_idx_aln)):
            ref_infos = region_info.get(chain)
            if chain_res_idx_aln[index] in chain_res_idx: # res index not '-'
                length_fill += 1
                # put designable statistics after getting the missing fc value
            else: # missing situations
                if ref_infos.get("chain_type") == "Antigen":
                    length_fill += 1
                    missing_all += 1
                else:
                    fv_e = ref_infos.get("Fv_end_idx")
                    if index < fv_e: # missing_fv
                        missing_fv += 1
                        missing_all += 1
                        # determine if located in CDR regions
                        for pos in ["cdr1_idx", "cdr2_idx", "cdr3_idx"]:
                            start, end = region_info[chain].get(pos)
                            if index in list(range(start, end)):
                                pass
                            else:
                                length_fill += 1
                    else:
                        missing_fc += 1
                        missing_all += 1

        if ref_infos.get("chain_type") != "Antigen" and missing_fc > 2:
            designable_indices = chain_res_idx_aln[:fv_e]
        elif ref_infos.get("chain_type") != "Antigen" and missing_fc <= 2:
            designable_indices = chain_res_idx_aln
        designable_indices = [d for d in designable_indices if d != "-"]
        chain_parsed["length_fill"] = length_fill
        chain_parsed["missing_fv"] = missing_fv
        chain_parsed["missing_fc"]  = missing_fc
        chain_parsed["missing_all"] = missing_all
        chain_parsed["designable_indices"] = designable_indices

        chain_info_list[chain] = chain_parsed


    return chain_info_list


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("--input_json", type=str, required=True)
    parser.add_argument("--chain_save", type=str, required=True)
    args = parser.parse_args()

    try:
        chains_info = verif(args.input_json)
        with open(args.chain_save, "w") as fh:
            fh.write(json.dumps(chains_info,  indent=4))

    except Exception as e:
        #status_json = args.chain_save.replace(args.chain_save, "job_status.json")
        status_json = os.path.join(os.path.dirname(args.chain_save), "job_status.json")
        msg = {"status": "failed", 
               "job_fail_reason": "Structure file parsing failed. Please select again.",
               "job_fail_detail_reason": str(e)}
               
        with open(status_json, "w") as out:
            out.write(json.dumps(msg, indent=4))

        print("构象解析失败。请重新选择！")
        exit(1)
        

