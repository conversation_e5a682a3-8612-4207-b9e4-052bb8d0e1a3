package models

import (
	"context"
	"time"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

const (
	TrialTypeTime int = 10
	TrialTypeNum  int = 20

	TrialStatusLose int = 1
)

// 表结构
type Trial struct {
	ID        uint64    `gorm:"id"`
	UserId    uint64    `gorm:"user_id"`
	Type      uint64    `gorm:"type"`
	TaskType  uint64    `gorm:"task_type"`
	UsedNum   uint64    `gorm:"used_num"`
	LimitNum  uint64    `gorm:"limit_num"`
	Status    int64     `gorm:"status"`
	StartTime time.Time `gorm:"start_time"`
	EndTime   time.Time `gorm:"login_time"`
	CreatedAt time.Time `gorm:"created_at"`
	UpdatedAt time.Time `gorm:"updated_at"`
}

func (t *Trial) TableName() string {
	return "helix_trial"
}

// 获取试用信息
func (t *Trial) GetTrail(ctx context.Context, userId int64, taskType ...int64) ([]Trial, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"user_id": userId,
		"status":  StatusNor,
	}
	if len(taskType) > 0 {
		condWhere["task_type"] = taskType[0]
	}

	// 执行查询
	var trialList []Trial
	err := db.Where(condWhere).Find(&trialList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return trialList, err
	}

	return trialList, nil
}

// 修改试用数据
func (t *Trial) Save(ctx context.Context) error {
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 获取试用信息
func (t *Trial) GetTrailByID(ctx context.Context, trialID uint64) (Trial, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]interface{}{
		"id": trialID,
	}

	// 执行查询
	var trial Trial
	if err := db.Where(condWhere).Find(&trial).Error; err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())

		return trial, err
	}

	return trial, nil
}
