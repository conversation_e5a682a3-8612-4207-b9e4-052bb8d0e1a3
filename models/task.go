package models

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"

	"icode.baidu.com/helix_web/helpers"
	"icode.baidu.com/helix_web/library/resource"
)

// 任务运行状态
const (
	TaskStatusNew    int = 1  // 新任务
	TaskStatusDoing  int = 10 // 运行中
	TaskStatusCancel int = 20 // 取消
	TaskStatusSucc   int = 30 // 完成
	TaskStatusFailed int = 40 // 失败

	IsExampleTrue  int = 1
	IsExampleFalse int = 0
)

const (
	TaskTypeLinearFold        int = 10  // liner-fold
	TaskTypeLinearPartition   int = 11  // liner-partition
	TaskTypeLinearDesign      int = 20  // 序列设计   LinearDesign
	TaskTypeUTR               int = 25  // 5utr
	TaskTypeAdmet             int = 30  // admet (支持训练)
	TaskTypeSelfAdmet         int = 31  // admet (自训练)
	TaskTypeMolActivity       int = 40  // 分子活性预测（序列）(支持训练)
	TaskTypeMolActStruct      int = 41  // 分子活性预测（结构）
	TaskTypeMMGBSA            int = 42  // MMGBSA
	TaskTypeHelixFoldAA       int = 43  // HelixFoldAA
	TaskTypeHF3Agab           int = 44  // HF3Agab
	TaskTypeMiniProteinDesign int = 45  // MiniProteinDesign
	TaskTypeAntibodyDesign    int = 46  // AntibodyDesign
	TaskTypeHelixFold3S1      int = 47  // HelixFold3-S1
	TaskTypeMolFormation      int = 50  // 化合物设计（分子生成-基础生成）
	TaskTypeMolFormPath       int = 53  // 化合物设计（分子生成-合成路径）
	TaskTypeHelixVSSyn        int = 55  // 基于骨架（分子生成）
	TaskTypeProtein           int = 60  // 蛋白质预测
	TaskTypeProteinSingle     int = 61  // 蛋白质预测(序列)
	TaskTypeProteinRelaxation int = 62  // 蛋白质Relaxation预测
	TaskTypeProteinComplex    int = 65  // 蛋白复合物
	TaskTypeVirtualFilter     int = 70  // 虚拟筛选（分子对接）
	TaskTypeVirtualVS         int = 71  // 虚拟筛选（）helixVS
	TaskTypeHelixDock         int = 72  // HelixDock
	TaskTypeDoubleDrug        int = 80  // 双药联合
	TaskTypeAntibody          int = 90  // 抗原
	TaskTypeNewCrown          int = 91  // 新冠
	TaskTypeProteinMutation   int = 92  // 蛋白突变
	TaskTypeKYKT              int = 93  // 抗原抗体
	TaskTypeProteinFunc       int = 100 // 蛋白质功能
	TaskTypeExactDrug         int = 105 // 精准用药
	TaskTypeCompound          int = 110 // 分子逆合成
)

var TaskTypeList = map[int]bool{
	TaskTypeLinearFold:      true,
	TaskTypeLinearPartition: true,
	TaskTypeLinearDesign:    true,
	TaskTypeUTR:             true,
	TaskTypeAdmet:           true,
	TaskTypeSelfAdmet:       true,
	TaskTypeMolActivity:     true,
	TaskTypeMolActStruct:    true,
	TaskTypeMMGBSA:          true,
	TaskTypeMolFormation:    true,
	TaskTypeMolFormPath:     true,
	TaskTypeHelixVSSyn:      true,
	TaskTypeProtein:         true,
	TaskTypeProteinSingle:   true,
	TaskTypeProteinComplex:  true,
	TaskTypeVirtualFilter:   true,
	TaskTypeVirtualVS:       true,
	TaskTypeHelixDock:       true,
	TaskTypeDoubleDrug:      true,
	TaskTypeAntibody:        true,
	TaskTypeNewCrown:        true,
	TaskTypeProteinMutation: true,
	TaskTypeKYKT:            true,
	TaskTypeProteinFunc:     true,
	TaskTypeExactDrug:       true,
	TaskTypeCompound:        true,
	TaskTypeHF3Agab:         true,
}

var TrainTaskTypeList = map[int]bool{
	TaskTypeAdmet:       true,
	TaskTypeSelfAdmet:   true,
	TaskTypeMolActivity: true,
}

const (
	FuncTypeTrain    int = 10 // 训练
	FuncTypeForecast int = 20 // 预测
)

const (
	TrainTypeReg = "regression"
	TrainTypeCla = "classify"
)

// 基于方式
const (
	BasedTypeTarget = "target"
	BasedTypeLigand = "ligand"
)

// pocket source 方式
const (
	PocketSourceSearch  = "auto_search"
	PocketSourceUser    = "user_defined"
	PocketSourceExample = "example"
)

// mol protein type
const (
	MolProteinTypeTarget    = "target"
	MolProteinTypeNotTarget = "not_target"
)

const (
	// 是否已读
	HadReadTrue  int = 1
	HadReadFalse int = 0

	ChargeTabNo      int = 0
	ChargeTabCoupon  int = 10
	ChargeTabBilling int = 11
	ChargeTabFinish  int = 20
)

const DefaultParentId int = 0

// 调度支持的最大VS 任务运行中的数量
const MaxHelixVSTaskDoingCount = 2

var CHPCTask = map[int]struct{}{
	TaskTypeHelixFoldAA:       {},
	TaskTypeVirtualVS:         {},
	TaskTypeLinearDesign:      {},
	TaskTypeHelixVSSyn:        {},
	TaskTypeMiniProteinDesign: {},
	TaskTypeAntibodyDesign:    {},
	TaskTypeHF3Agab:           {},
	TaskTypeHelixFold3S1:      {},
	TaskTypeLinearFold:        {},
	TaskTypeLinearPartition:   {},
}

// 表结构
type Task struct {
	ID              uint64    `gorm:"id"`
	UserId          uint64    `gorm:"user_id"`
	IAMUserID       string    `gorm:"iam_user_id"`
	IAMUserDomainID string    `gorm:"iam_user_domain_id"`
	Type            uint64    `gorm:"type"`
	FuncType        uint64    `gorm:"func_type"`
	Name            string    `gorm:"name"`
	Config          string    `gorm:"config"`
	Result          string    `gorm:"result"`
	ServerTaskId    uint64    `gorm:"server_task_id"`
	IsExample       uint64    `gorm:"is_example"`
	IsApi           uint64    `gorm:"is_api"`
	Status          int64     `gorm:"status"`
	UseTime         time.Time `gorm:"use_time"`
	ChargeStartTime time.Time `gorm:"charge_start_time"`
	ChargeEndTime   time.Time `gorm:"charge_end_time"`
	FinishTime      time.Time `gorm:"finish_time"`
	Resource        string    `gorm:"resource"`
	ParentID        uint64    `gorm:"parent_id"`
	HadRead         uint64    `gorm:"had_read"`
	ChargeTab       uint64    `gorm:"charge_tab"`
	CreatedAt       time.Time `gorm:"created_at"`
	UpdatedAt       time.Time `gorm:"updated_at"`
	JobFailReason   string    `gorm:"job_fail_reason"`
	NTokens         uint64    `gorm:"n_tokens"`
	Coupons         string    `gorm:"coupons"`
	Balance         float64   `gorm:"balance"`
	OrderID         string    `gorm:"order_id"`
}

// check 付费模块
func CheckTaskChargeType(tType int) bool {
	if tType == TaskTypeLinearDesign ||
		tType == TaskTypeProtein ||
		tType == TaskTypeProteinSingle ||
		tType == TaskTypeAdmet ||
		tType == TaskTypeProteinComplex ||
		tType == TaskTypeUTR ||
		tType == TaskTypeLinearFold ||
		tType == TaskTypeLinearPartition ||
		tType == TaskTypeKYKT ||
		tType == TaskTypeHelixFoldAA ||
		tType == TaskTypeHelixFold3S1 ||
		tType == TaskTypeHF3Agab ||
		tType == TaskTypeHelixVSSyn {
		return true
	}
	return false
}

// config 字段
type TaskConfig struct {
	Serial           string  `json:"serial"`
	SerialLen        int64   `json:"serial_len"`
	BeamSize         int64   `json:"beam_size"`
	ModelPath        string  `json:"model_path"`
	PdbURL           string  `json:"pdb_url"`
	PdbName          string  `json:"pdb_name"`
	FileURL          string  `json:"file_url"`
	Level            int64   `json:"level"`
	ProteinURL       string  `json:"protein_url"`
	MolName          string  `json:"mol_name"`
	MolURL           string  `json:"mol_url"`
	GbModel          int64   `json:"gb_model"`
	SimulationLen    int64   `json:"simulation_len"`
	Epsilon          int64   `json:"epsilon"`
	BillingUnitCount float64 `json:"billing_unit_count"`
	TaskFoldConf
	LinearDesignTaskConf
	TaskVirtualConf
	TaskMolFormationConf
	TaskAdmetConf
	TaskMolActivityConf
	TaskDoubleDrugConf
	TaskProteinConf
	TaskAntigenConf
	TaskProteinMutationConf
	TaskMolActStructConf
	TaskExactDrugConf
	TaskUTRConf
	TaskHelixFoldAAConf
	TaskHF3AgabConf
	// TaskMiniProteinDesignConf
	JobName         string `json:"job_name"`
	TargetSequence  string `json:"target_sequence"`
	BinderMinLength int64  `json:"binder_min_length"`
	BinderMaxLength int64  `json:"binder_max_length"`
	DesignMode      string `json:"design_mode"`
	// TaskAntibodyDesignConf
	DesignNum    int                   `json:"design_num"`
	Diverse      bool                  `json:"diverse"`
	Reference    string                `json:"reference"`
	DesignChains []AntibodyDesignChain `json:"design_chains"`
	// LinearDesign
	Name            string                `json:"name"`
	Sequence        string                `json:"sequence"`
	SequenceFileURL string                `json:"sequence_file_url"`
	SequenceType    string                `json:"sequence_type"`
	Sequence5UTR    string                `json:"sequence_5utr,omitempty"`
	Sequence3UTR    string                `json:"sequence_3utr,omitempty"`
	ParamCDS        LinearDesignParamCDS  `json:"param_cds"`
	Param5UTR       LinearDesignParam5UTR `json:"param_5utr"`
	// LinearFold LinearPartition
	NeedFold        bool                   `json:"need_fold"`
	NeedPartition   bool                   `json:"need_partition"`
	FoldConfig      *LinearFoldConfig      `json:"fold_config,omitempty"`
	PartitionConfig *LinearPartitionConfig `json:"partition_config,omitempty"`
}

// config 通用conf
type TaskCommonConf struct {
	FileUrl   string `json:"file_url"`
	Serial    string `json:"serial"`
	SerialLen int64  `json:"serial_len"`
}

type TaskMMGBSAConf struct {
	ProteinUrl    string `json:"protein_url"`
	MolUrl        string `json:"mol_url"`
	MolName       string `json:"mol_name"`
	GbModel       int64  `json:"gb_model"`
	SimulationLen int64  `json:"simulation_len"`
	Epsilon       int64  `json:"epsilon"`
}

type TaskUTRConf struct {
	FileUrl    string `json:"file_url"`
	Serial     string `json:"serial"`
	TargetLen  int64  `json:"target_len"`
	GivenUtr   bool   `json:"given_utr"`
	UtrSerial  string `json:"utr_serial"`
	UtrFileUrl string `json:"utr_file_url"`
	Level      int64  `json:"level"`
}

type TaskFoldConf struct {
	TaskCommonConf
	BeamSize   int64 `json:"beam_size"`
	Constraint bool  `json:"constraint"`
	Score      int64 `json:"score"`
}

type TaskHelixFoldAAConf struct {
	RandomSeed         int64                           `json:"random_seed"`
	Recycle            int64                           `json:"recycle"`
	Ensemble           int64                           `json:"ensemble"`
	ModelType          string                          `json:"model_type"`
	S1SampleConstraint []HelixFoldAAS1SampleConstraint `json:"s1_sample_constraint"`
	Entities           []HelixFoldAAEntity             `json:"entities"`
	Constraints        []HelixFoldAAConstraint         `json:"constraints"`
	RefStructures      []HelixFoldAARefStructure       `json:"ref_structures"`
	Tokens             uint64                          `json:"tokens"`
}

type TaskMiniProteinDesignConf struct {
	JobName         string `json:"job_name"`
	TargetSequence  string `json:"target_sequence"`
	BinderMinLength int64  `json:"binder_min_length"`
	BinderMaxLength int64  `json:"binder_max_length"`
	DesignMode      string `json:"design_mode"`
	Tokens          uint64 `json:"tokens"`
}

type TaskAntibodyDesignConf struct {
	JobName          string                `json:"job_name"`
	DesignNum        int                   `json:"design_num"`
	DesignMode       string                `json:"design_mode"`
	Diverse          bool                  `json:"diverse"`
	Reference        string                `json:"reference"`
	DesignChains     []AntibodyDesignChain `json:"design_chains"`
	Tokens           uint64                `json:"tokens"`
	BillingUnitCount float64               `json:"billing_unit_count"`
}

type HelixFoldAAEntity struct {
	Type          string               `json:"type,omitempty"`
	Count         int64                `json:"count,omitempty"`
	Sequence      string               `json:"sequence,omitempty"`
	Ccd           string               `json:"ccd,omitempty"`
	Smiles        string               `json:"smiles,omitempty"`
	Modifications []ModificationEntity `json:"modification,omitempty"`
}

type ModificationEntity struct {
	Type        string `json:"type,omitempty"`
	Index       int64  `json:"index"`
	Ccd         string `json:"ccd,omitempty"`
	Expression  string `json:"expression,omitempty"`
	RSmiles     string `json:"R_smiles,omitempty"`
	RConnectIdx int64  `json:"R_connect_idx"`
}

type HelixFoldAAConstraint struct {
	Type        string `json:"type,omitempty"`
	Level       string `json:"level,omitempty"`
	LeftEntity  string `json:"left_entity,omitempty"`
	RightEntity string `json:"right_entity,omitempty"`
	Distance    int64  `json:"distance,omitempty"`
}

type HelixFoldAARefStructure struct {
	RefFile          string                       `json:"ref_file,omitempty"`
	ReferTargetPairs []HelixFoldAAReferTargetPair `json:"refer_target_pairs,omitempty"`
}

type HelixFoldAAS1SampleConstraint struct {
	LeftEntity  string `json:"left_entity,omitempty"`
	RightEntity string `json:"right_entity,omitempty"`
}

type HelixFoldAAReferTargetPair struct {
	Refer  string `json:"refer"`
	Target string `json:"target"`
}

type AntibodyDesignChain struct {
	ChainID string `json:"chain_ID"`
	Region  []any  `json:"region"`
}

type TaskHF3AgabConf struct {
	TaskType int    `json:"task_type"`
	RunMode  string `json:"run_mode"`
	FileUrl  string `json:"file_url"`
	Smiles   string `json:"smiles"`
	DataType string `json:"data_type"`
}

// 虚拟筛选conf
type TaskVirtualConf struct {
	TaskCommonConf
	PdbURL           string  `json:"pdb_url"`
	PdbName          string  `json:"pdb_name"`
	CenterX          float64 `json:"center_x"`
	CenterY          float64 `json:"center_y"`
	CenterZ          float64 `json:"center_z"`
	SizeX            float64 `json:"size_x"`
	SizeY            float64 `json:"size_y"`
	SizeZ            float64 `json:"size_z"`
	PocketSource     string  `json:"pocket_source"`
	ProteinType      string  `json:"protein_type"`
	MolNum           int64   `json:"mol_num"`
	DesignMolNum     int64   `json:"design_mol_num"`
	FilterStr        string  `json:"filter_str"`
	FilterLib        string  `json:"input_ligand_library"`
	IndexList        []int64 `json:"index_list"`
	Cost             float64 `json:"cost"`
	BillingUnitCount float64 `json:"billing_unit_count"`
}

type TaskMolFormationConf struct {
	TaskVirtualConf
	BasedType     string  `json:"based_type"`
	PdbUrl2       string  `json:"pdb_url2"`
	PdbName2      string  `json:"pdb_name2"`
	CenterX2      float64 `json:"center_x2"`
	CenterY2      float64 `json:"center_y2"`
	CenterZ2      float64 `json:"center_z2"`
	SizeX2        float64 `json:"size_x2"`
	SizeY2        float64 `json:"size_y2"`
	SizeZ2        float64 `json:"size_z2"`
	PocketSource2 string  `json:"pocket_source2"`
	ProteinType2  string  `json:"protein_type2"`
}

type TaskAdmetConf struct {
	TaskCommonConf
	TrainType string  `json:"train_type"`
	ModelPath string  `json:"model_path"`
	Level     int64   `json:"level"`
	Number    int64   `json:"number"`
	ModelList []int64 `json:"model_list"`
}

type TaskMolActivityConf struct {
	TaskCommonConf
	ModelPath     string `json:"model_path"`
	Protein       string `json:"protein"`
	UseBingdingdb bool   `json:"use_bingdingdb"`
	UseChembl     bool   `json:"use_chembl"`
}

type TaskMolActStructConf struct {
	MolUrl     string `json:"mol_url"`
	ProteinUrl string `json:"protein_url"`
}

type TaskProteinConf struct {
	TaskCommonConf
	PdbUrl     string `json:"pdb_url"`
	PdbName    string `json:"pdb_name"`
	IsCameo    bool   `json:"is_cameo"`
	IsCasp     bool   `json:"is_casp"`
	Ensembling int64  `json:"ensembling"`
	Relaxation bool   `json:"relaxation"`
	Fold2      bool   `json:"fold2"`
	Level      int64  `json:"level"`
}

type TaskProteinSingleConf struct {
	TaskCommonConf
	Level int64 `json:"level"`
}

type TaskProteinFuncConf struct {
	PdbUrl  string `json:"pdb_url"`
	PdbName string `json:"pdb_name"`
}

type TaskProteinRelaxationConf struct {
	ProteinUrl string `json:"protein_url"`
}

type TaskDoubleDrugConf struct {
	DrugA    string `json:"drug_a"`
	DrugB    string `json:"drug_b"`
	FileUrl  string `json:"file_url"`
	CellLine string `json:"cell_line"`
	Tissues  string `json:"tissues"`
}

type TaskAntigenConf struct {
	HeavySerial   string `json:"heavy_serial"`
	LightSerial   string `json:"light_serial"`
	AntigenSerial string `json:"antigen_serial"`
	FileUrl       string `json:"file_url"`
}

type TaskProteinMutationConf struct {
	Protein1 string `json:"protein1"`
	Protein2 string `json:"protein2"`
	Point1   string `json:"point1"`
	Point2   string `json:"point2"`
	FileUrl  string `json:"file_url"`
}

type TaskExactDrugConf struct {
	DrugList  string `json:"drug_list"`
	MolUrl    string `json:"mol_url"`
	Serial    string `json:"serial"`
	Sample    string `json:"sample"`
	SampleUrl string `json:"sample_url"`
}

// result 中字段
type TaskRes struct {
	FileUrl     string `json:"file_url"`
	DownloadUrl string `json:"download_url"`
	ErrorCode   int64  `json:"error_code"`
}

// task search
type TaskCond struct {
	UserId      int64    `json:"user_id"`
	IAMUserIDs  []string `json:"iam_user_ids"`
	TaskIDList  []int64  `json:"task_id_list"`
	TypeList    []int64  `json:"type_list"`
	FuncType    int64    `json:"func_type"`
	StatusList  []int64  `json:"status_list"`
	ParentID    []int64  `json:"parent_id"`
	HadRead     []int64  `json:"had_read"`
	ChargeTab   []int64  `json:"charge_tab"`
	BasedType   string   `json:"based_type"`
	Keyword     string   `json:"keyword"`
	LtCreatedAt string   `json:"lt_created_at"`
	GtCreatedAt string   `json:"gt_created_at"`
}

// 表名
func (t *Task) TableName() string {
	return "helix_task"
}

// 获取任务信息
func (t *Task) GetTaskById(ctx context.Context, taskId int64) (Task, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)

	// 拼接where
	condWhere := map[string]any{
		"id": taskId,
	}

	// 执行查询
	taskData := Task{}
	err := db.Where(condWhere).First(&taskData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return taskData, err
	}
	return taskData, nil
}

// 获取任务信息
func (t *Task) GetTaskByUserAndId(ctx context.Context, userId, taskId int64) (Task, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)

	// 拼接where
	condWhere := map[string]any{
		"id":      taskId,
		"user_id": userId,
	}

	// 执行查询
	taskData := Task{}
	err := db.Where(condWhere).First(&taskData).Error
	if err != nil && err != gorm.ErrRecordNotFound {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return taskData, err
	}
	return taskData, nil
}

// 通过taskId 获取任务list
func (t *Task) GetListByIds(ctx context.Context, taskIds []int64, orderBy string) ([]Task, error) {
	db := resource.Gorm.WithContext(ctx)
	db = db.Where("status <> ?", StatusDel)

	// 拼接where
	condWhere := map[string]any{
		"id": taskIds,
	}

	// 执行查询
	var taskDataList []Task
	err := db.Where(condWhere).Order(orderBy).Find(&taskDataList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskDataList, err
}

func (t *Task) GetAllByIds(ctx context.Context, taskIds []int64, orderBy string) ([]Task, error) {
	db := resource.Gorm.WithContext(ctx)

	// 拼接where
	condWhere := map[string]any{
		"id": taskIds,
	}

	// 执行查询
	var taskDataList []Task
	err := db.Where(condWhere).Order(orderBy).Find(&taskDataList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskDataList, err
}

// 查询列表
func (t *Task) GetTaskByCond(ctx context.Context, cond TaskCond, limit, page int, orderBy ...string) ([]Task, error) {
	db := resource.Gorm.WithContext(ctx)

	// db where
	db = taskBuildWhere(db, cond)

	// 排序处理
	order := "id desc"
	if len(orderBy) > 0 {
		order = orderBy[0]
	}

	// 执行查询
	var taskDataList []Task
	offset := limit * (page - 1)
	err := db.Limit(limit).Offset(offset).Order(order).Find(&taskDataList).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskDataList, err
}

// 统计数据
func (t *Task) CountTaskByCond(ctx context.Context, cond TaskCond) (int64, error) {
	db := resource.Gorm.WithContext(ctx)

	// db where
	db = taskBuildWhere(db, cond)

	// 执行查询
	var count int64
	err := db.Model(t).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return count, err
}

// 添加任务
func (t *Task) Add(ctx context.Context, taskNew Task, status ...int) (Task, error) {
	taskNew.Status = int64(TaskStatusNew)
	taskNew.UseTime = time.Now()
	taskNew.CreatedAt = time.Now()
	taskNew.UpdatedAt = time.Now()

	if len(status) > 0 {
		taskNew.Status = int64(status[0])
	}
	db := resource.Gorm.WithContext(ctx)
	err := db.Create(&taskNew).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}
	return taskNew, err
}

// 保存任务
func (t *Task) Save(ctx context.Context) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Updates(&t).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 批量更新
func (t *Task) BatchUpdate(ctx context.Context, ids []uint64, updateData map[string]any) error {
	// 获取实例
	db := resource.Gorm.WithContext(ctx)
	err := db.Model(t).Where("id in (?)", ids).Updates(updateData).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
	}

	return err
}

// 数据转化
func (t *Task) SwapData() map[string]any {
	taskData := make(map[string]any)
	if t.ID <= 0 {
		return taskData
	}

	// 处理数据
	var config map[string]any
	_ = json.Unmarshal([]byte(t.Config), &config)

	var result map[string]any
	_ = json.Unmarshal([]byte(t.Result), &result)

	// status 转化下
	status := t.Status
	if status == int64(TaskStatusNew) {
		status = int64(TaskStatusDoing)
	}
	taskData = map[string]any{
		"task_id":           t.ID,
		"user_id":           t.UserId,
		"name":              t.Name,
		"type":              t.Type,
		"func_type":         t.FuncType,
		"status":            status,
		"config":            config,
		"result":            result,
		"is_example":        t.IsExample,
		"use_time":          SwapFieldUnix(t.UseTime.Unix()),
		"charge_start_time": SwapFieldUnix(t.ChargeStartTime.Unix()),
		"charge_end_time":   SwapFieldUnix(t.ChargeEndTime.Unix()),
		"finish_time":       SwapFieldUnix(t.FinishTime.Unix()),
		"resource":          t.Resource,
		"parent_id":         t.ParentID,
		"had_read":          t.HadRead,
		"created_at":        t.CreatedAt.Unix(),
		"updated_at":        t.UpdatedAt.Unix(),
		"job_fail_reason":   t.JobFailReason,
		"is_api":            t.IsApi,
	}
	return taskData
}

// where build
func taskBuildWhere(db *gorm.DB, cond TaskCond) *gorm.DB {
	if len(cond.IAMUserIDs) > 0 {
		db = db.Where("(iam_user_id in (?) OR user_id = ?)", cond.IAMUserIDs, cond.UserId)
	} else {
		if cond.UserId > 0 {
			db = db.Where("user_id = ?", cond.UserId)
		}
	}
	if len(cond.TaskIDList) > 0 {
		db = db.Where("id in (?)", cond.TaskIDList)
	}
	if len(cond.TypeList) > 0 {
		db = db.Where("type in (?)", cond.TypeList)
	}
	if cond.FuncType > 0 {
		db = db.Where("func_type = ?", cond.FuncType)
	}
	if len(cond.StatusList) > 0 {
		db = db.Where("status in (?)", cond.StatusList)
	} else {
		db = db.Where("status <> ?", StatusDel)
	}
	if len(cond.ParentID) > 0 {
		db = db.Where("parent_id in (?)", cond.ParentID)
	}
	if len(cond.HadRead) > 0 {
		db = db.Where("had_read in (?)", cond.HadRead)
	}
	if len(cond.ChargeTab) > 0 {
		db = db.Where("charge_tab in (?)", cond.ChargeTab)
	}
	if len(cond.Keyword) > 0 {
		db = db.Where("(id = ? OR name like ?)", cond.Keyword, "%"+cond.Keyword+"%")
	}
	if len(cond.LtCreatedAt) > 0 {
		db = db.Where("created_at < ?", cond.LtCreatedAt)
	}
	if len(cond.GtCreatedAt) > 0 {
		db = db.Where("created_at > ?", cond.GtCreatedAt)
	}
	return db
}

// GetTaskCount 根据taskType 获取运行中的任务数量
func (t *Task) GetTaskCount(ctx context.Context, taskType int) (int64, error) {
	db := resource.Gorm.WithContext(ctx)

	condWhere := map[string]any{
		"type":   taskType,
		"status": int64(TaskStatusDoing),
	}

	// 执行查询
	var count int64
	err := db.Model(Task{}).Where(condWhere).Count(&count).Error
	if err != nil {
		helpers.DBLogError(ctx, err)
		go helpers.HelixNotice(ctx, err.Error())
		return 0, err
	}
	return count, nil
}
